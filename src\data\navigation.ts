import { Building, CalendarRange, ChartAreaIcon, ChartColumn, Dot, File, GraduationCap, LayoutDashboard, MessageCircle, NotebookPen, Settings, User2, Users2 } from "lucide-react";
import { ACTIONS, createNavigationPermissions, STANDARD_ACTIONS, VIEW_ONLY } from "./permissions";

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ElementType<any, keyof React.JSX.IntrinsicElements>;
  href: string;
  permissions: string[];
  children?: NavigationItem[];
  badge?: string;
  isExact?: boolean;
}

const SIDEBAR_ICONS = {
  dashboard: LayoutDashboard,
}

export const NAVIGATION_CONFIG: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/dashboard',
    permissions: [], // Everyone can access dashboard
    icon: LayoutDashboard,
  },
  {
    id: 'academic',
    label: 'Academic',
    icon: File,
    href: '/academic',
    permissions: createNavigationPermissions('academic', VIEW_ONLY),
    children: [
      {
        id: 'department',
        label: 'Department',
        icon: Dot,
        href: '/academics/departments',
        permissions: createNavigationPermissions('department', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'session',
        label: 'Session',
        icon: Dot,
        href: '/academics/sessions',
        permissions: createNavigationPermissions('session', STANDARD_ACTIONS),
      },
      {
        id: 'subject',
        label: 'Subject',
        icon: Dot,
        href: '/academics/subjects',
        permissions: createNavigationPermissions('subject', STANDARD_ACTIONS),
      },
      {
        id: 'time-table',
        label: 'Time Table',
        icon: Dot,
        href: '/academics/time-tables',
        permissions: createNavigationPermissions('timetable', STANDARD_ACTIONS),
      },
      {
        id: 'certificate',
        label: 'Certificate',
        icon: Dot,
        href: '/academics/certificates',
        permissions: createNavigationPermissions('certificate', STANDARD_ACTIONS),
      },
      {
        id: 'id-card',
        label: 'ID Card',
        icon: Dot,
        href: '/academics/id-cards',
        permissions: createNavigationPermissions('id_card', STANDARD_ACTIONS),
      },
    ],
  },
  {
    id: 'staff',
    label: 'Staff',
    icon: Users2,
    href: '/staff',
    permissions: createNavigationPermissions('staff', VIEW_ONLY),
    children: [
      {
        id: 'staff',
        label: 'Staffs',
        icon: Dot,
        href: '/staffs/staffs',
        permissions: createNavigationPermissions('staff', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'department',
        label: 'Department',
        icon: Dot,
        href: '/staffs/departments',
        permissions: createNavigationPermissions('department', STANDARD_ACTIONS),
      },
      {
        id: 'designation',
        label: 'Designation',
        icon: Dot,
        href: '/staffs/designations',
        permissions: ['designation.manage', 'designation.view'],
      },
      {
        id: 'attendance',
        label: 'Attendance',
        icon: Dot,
        href: '/staffs/attendances',
        permissions: ['attendance.manage', 'attendance.view_all', 'attendance.view', 'attendance.take'],
      },
      {
        id: 'leave',
        label: 'Leave',
        icon: Dot,
        href: '/staffs/leaves',
        permissions: ['leave.manage', 'leave.view'],
      },
      {
        id: 'payroll',
        label: 'Payroll',
        icon: Dot,
        href: '/staffs/payrolls',
        permissions: ['payroll.manage', 'payroll.view'],
      },
    ],
  },
  {
    id: 'student',
    label: 'Student',
    icon: Users2,
    href: '/students',
    permissions: ['students.view'],
    children: [
      {
        id: 'student',
        label: 'Students',
        icon: Dot,
        href: '/students/students',
        permissions: ['student.manage', 'student.view'],
        isExact: true,
      },
      {
        id: 'registration',
        label: 'Registration',
        icon: Dot,
        href: '/students/registrations',
        permissions: ['registration.manage', 'registration.view'],
        isExact: true,
      },
      {
        id: 'health-record',
        label: 'Health Record',
        icon: Dot,
        href: '/students/health-records',
        permissions: ['health_record.manage', 'health_record.view'],
      },
      {
        id: 'attendance',
        label: 'Sttendance',
        icon: Dot,
        href: '/students/attendances',
        permissions: ['attendance.manage', 'attendance.view'],
      },
      {
        id: 'promotion',
        label: 'Promotion',
        icon: Dot,
        href: '/students/promotions',
        permissions: ['promotion.manage', 'promotion.view'],
      },
      {
        id: 'fee-allocation',
        label: 'Fee Allocation',
        icon: Dot,
        href: '/students/fee-allocations',
        permissions: ['fee_allocation.manage', 'fee_allocation.view'],
      },
      {
        id: 'leave-request',
        label: 'Leave Request',
        icon: Dot,
        href: '/students/leave-requests',
        permissions: ['leave_request.manage', 'leave_request.view'],
      },
      {
        id: 'report',
        label: 'Report',
        icon: Dot,
        href: '/students/reports',
        permissions: ['report.manage', 'report.view'],
      },
    ],
  },
  {
    id: 'role',
    label: 'Roles',
    icon: User2,
    href: '/roles',
    permissions: ['role.view'],
    children: [
      {
        id: 'role',
        label: 'Role',
        icon: Dot,
        href: '/roles',
        permissions: ['role.manage', 'role.view'],
        isExact: true,
      },
      {
        id: 'permission',
        label: 'Permission',
        icon: Dot,
        href: '/roles/permissions',
        permissions: ['permission.manage', 'permission.view'],
      },
    ],
  },
  {
    id: 'classes',
    label: 'Classes',
    icon: Building,
    href: '/classes',
    permissions: ['classes.read'],
    children: [
      {
        id: 'classes_list',
        label: 'All Classes',
        icon: Dot,
        href: '/classes',
        permissions: ['classes.read'],
        isExact: true,
      },
      {
        id: 'classes_add',
        label: 'Create Class',
        icon: Dot,
        href: '/classes/add',
        permissions: ['classes.create'],
      },
    ],
  },
  {
    id: 'grades',
    label: 'Grades',
    icon: GraduationCap,
    href: '/grades',
    permissions: ['grades.view_all', 'grades.view_own', 'grades.view_children', 'grades.view_class'],
  },
  {
    id: 'user',
    label: 'Users',
    icon: User2,
    href: '/users',
    permissions: ['user.view', 'user.manage'],
  },
  {
    id: 'attendance',
    label: 'Attendance',
    icon: CalendarRange,
    href: '/attendance',
    permissions: ['attendance.view_all', 'attendance.view_own', 'attendance.view_children', 'attendance.view_class'],
  },
  {
    id: 'assignments',
    label: 'Assignments',
    icon: NotebookPen,
    href: '/assignments',
    permissions: ['assignments.view_all', 'assignments.view_own', 'assignments.view_children'],
  },
  {
    id: 'communications',
    label: 'Messages',
    icon: MessageCircle,
    href: '/communications',
    permissions: ['communication.view_all', 'communication.send_parents', 'communication.send_teachers'],
  },
  {
    id: 'fees',
    label: 'Fees',
    icon: ChartColumn,
    href: '/fees',
    permissions: ['fees.read', 'fees.view_own', 'fees.manage'],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: ChartAreaIcon,
    href: '/reports',
    permissions: ['reports.view', 'reports.generate'],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/settings',
    permissions: ['settings.manage'],
    children: [
      {
        id: 'settings_school',
        label: 'School Settings',
        icon: Dot,
        href: '/settings/school',
        permissions: ['settings.manage'],
      },
      {
        id: 'settings_roles',
        label: 'Manage Roles',
        icon: Dot,
        href: '/settings/roles',
        permissions: ['roles.manage'],
      },
      {
        id: 'settings_users',
        label: 'Manage Users',
        icon: Dot,
        href: '/settings/users',
        permissions: ['users.manage'],
      },
    ],
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: User2,
    href: '/profile',
    permissions: [], // Everyone can access their profile
  },
];

export function getFilteredNavigation(userPermissions: string[]): NavigationItem[] {
  return NAVIGATION_CONFIG.filter(item => {
    // If no permissions required, show to everyone
    if (item.permissions.length === 0) return true;
    
    // Check if user has at least one required permission
    return item.permissions.some(permission => userPermissions.includes(permission));
  }).map(item => ({
    ...item,
    children: item.children?.filter(child => {
      if (child.permissions.length === 0) return true;
      return child.permissions.some(permission => userPermissions.includes(permission));
    }),
  }));
}