// src/lib/auth/permissions.ts
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: string[];
  isSystemRole: boolean; // Cannot be deleted
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const ACTIONS = {
  VIEW: 'view',
  MANAGE: 'manage',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  TAKE: 'take',
  VIEW_ALL: 'view_all'
} as const;

export const STANDARD_ACTIONS = [ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE, ACTIONS.VIEW];
export const VIEW_ONLY = [ACTIONS.VIEW];
export const ATTENDANCE_ACTIONS = [ACTIONS.MANAGE, ACTIONS.VIEW, ACTIONS.VIEW_ALL, ACTIONS.TAKE];

function createPermission(resource: string, action: string, customDescription?: string): Permission {
  return {
    id: `${resource}.${action}`,
    name: `${action.charAt(0).toUpperCase() + action.slice(1)} ${resource.charAt(0).toUpperCase() + resource.slice(1)}`,
    resource,
    action,
    description: customDescription || `Can ${action} ${resource} information`
  };
}

function createResourcePermissions(resource: string, actions: string[], customDescriptions?: Record<string, string>): Permission[] {
  return actions.map(action => 
    createPermission(resource, action, customDescriptions?.[action])
  );
}

export function createNavigationPermissions(resource: string, actions: string[]): string[] {
  return actions.map(action => `${resource}.${action}`);
}

// Base permissions that can be assigned to any role
export const BASE_PERMISSIONS: Permission[] = [
  // Academic Management
  ...createResourcePermissions('academic', VIEW_ONLY),
  ...createResourcePermissions('department', STANDARD_ACTIONS),
  ...createResourcePermissions('session', STANDARD_ACTIONS),
  ...createResourcePermissions('subject', STANDARD_ACTIONS),
  ...createResourcePermissions('timetable', STANDARD_ACTIONS),
  ...createResourcePermissions('certificate', STANDARD_ACTIONS),
  ...createResourcePermissions('id_card', STANDARD_ACTIONS),
  
  // Staff Management
  ...createResourcePermissions('staff', VIEW_ONLY),
  ...createResourcePermissions('staffs', STANDARD_ACTIONS),
  ...createResourcePermissions('designation', STANDARD_ACTIONS),
  ...createResourcePermissions('attendance', ATTENDANCE_ACTIONS),
  ...createResourcePermissions('leave', STANDARD_ACTIONS),
  ...createResourcePermissions('payroll', STANDARD_ACTIONS),
  
  // Student Management
  ...createResourcePermissions('student', VIEW_ONLY),
  ...createResourcePermissions('students', STANDARD_ACTIONS),
  ...createResourcePermissions('registration', STANDARD_ACTIONS),
  ...createResourcePermissions('health_record', STANDARD_ACTIONS),
  ...createResourcePermissions('promotion', STANDARD_ACTIONS),
  ...createResourcePermissions('fee_allocation', STANDARD_ACTIONS),
  ...createResourcePermissions('leave_request', STANDARD_ACTIONS),
  ...createResourcePermissions('report', STANDARD_ACTIONS),
  
  // Role management
  ...createResourcePermissions('role', VIEW_ONLY),
  ...createResourcePermissions('role', STANDARD_ACTIONS),
  
  // Permission management
  ...createResourcePermissions('permission', STANDARD_ACTIONS),

  // User management
  ...createResourcePermissions('user', STANDARD_ACTIONS),
  
  // Class management
  ...createResourcePermissions('class', STANDARD_ACTIONS),
  
  // Grade management
  ...createResourcePermissions('grade', STANDARD_ACTIONS),

  // Communication
  { id: 'communication.view_all', name: 'View All Messages', description: 'Can view all communications', resource: 'Communication', action: 'read' },
  { id: 'communication.send_parents', name: 'Message Parents', description: 'Can send messages to parents', resource: 'Communication', action: 'parents' },
  { id: 'communication.send_teachers', name: 'Message Teachers', description: 'Can send messages to teachers', resource: 'Communication', action: 'teachers' },
  { id: 'communication.send_students', name: 'Message Students', description: 'Can send messages to students', resource: 'Communication', action: 'students' },
  
  // Report management
  ...createResourcePermissions('report', STANDARD_ACTIONS),

  // Financial
  ...createResourcePermissions('fees', VIEW_ONLY),
  ...createResourcePermissions('fees', STANDARD_ACTIONS),
  
  // Analytics
  ...createResourcePermissions('analytics', VIEW_ONLY),
  
  // System settings
  ...createResourcePermissions('settings', STANDARD_ACTIONS),
];

// Default system roles
export const SYSTEM_ROLES: Role[] = [
  {
    id: 'super_admin',
    name: 'super_admin',
    displayName: 'Super Admin',
    description: 'Super Administrator with all permissions',
    permissions: BASE_PERMISSIONS.map((p) => p.id),
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'admin',
    name: 'admin',
    displayName: 'School Admin',
    description: 'School Administrator with limited permissions',
    permissions: BASE_PERMISSIONS.map((p) => p.id),
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'teacher',
    name: 'teacher',
    displayName: 'Teacher',
    description: 'Teacher with classroom management permissions',
    permissions: [
      'students.read', 'students.update',
      'classes.read', 'classes.update',
      'grades.read', 'grades.create', 'grades.update',
      'reports.read', 'reports.create'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'student',
    name: 'student',
    displayName: 'Student',
    description: 'Student with limited view permissions',
    permissions: [
      'grades.read', 'classes.read'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'parent',
    name: 'parent',
    displayName: 'Parent',
    description: 'Parent with view permissions for their children',
    permissions: [
      'students.read', 'grades.read', 'classes.read', 'reports.read'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];